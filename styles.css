/* Modern POS Theme System - 2024 Standards */
:root {
    /* Light Mode - Default */
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-accent: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #334155;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --accent-primary: #0ea5e9;
    --accent-secondary: #0284c7;
    --accent-gradient: linear-gradient(135deg, #0ea5e9, #0284c7);
    --shadow-color: rgba(14, 165, 233, 0.1);
    --border-radius: 8px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    /* Brand Customization */
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* Dark Mode - Modern AI Interface (Keep Intact) */
.theme-dark {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-accent: #334155;
    --text-primary: #ffffff;
    --text-secondary: #e2e8f0;
    --text-muted: #cbd5e1;
    --border-color: #475569;
    --accent-primary: #06b6d4;
    --accent-secondary: #0891b2;
    --accent-gradient: linear-gradient(135deg, #06b6d4, #0891b2);
    --shadow-color: rgba(6, 182, 212, 0.2);
    --border-radius: 12px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #f87171;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* High Contrast - Accessibility Mode */
.theme-contrast {
    --bg-primary: #ffffff;
    --bg-secondary: #ffffff;
    --bg-accent: #f5f5f5;
    --text-primary: #000000;
    --text-secondary: #000000;
    --text-muted: #333333;
    --border-color: #000000;
    --accent-primary: #0066cc;
    --accent-secondary: #004499;
    --accent-gradient: linear-gradient(135deg, #0066cc, #004499);
    --shadow-color: rgba(0, 102, 204, 0.3);
    --border-radius: 4px;
    --success-color: #008000;
    --warning-color: #ff8c00;
    --error-color: #cc0000;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* High Contrast Button Styling */
.theme-contrast .theme-contrast-button:disabled {
    background-color: #cccccc !important;
    color: #000000 !important;
    border: 2px solid #000000 !important;
}

.theme-contrast #checkoutBtn:not(:disabled) {
    background-color: var(--accent-primary) !important;
    color: #ffffff !important;
    border: 2px solid #000000 !important;
}

.theme-contrast #checkoutBtn:not(:disabled):hover {
    background-color: var(--accent-secondary) !important;
    border-color: #000000 !important;
}

/* Business Theme CSS Variables */
/* Café Theme */
.theme-cafe {
    --bg-primary: #faf7f2;
    --bg-secondary: #ffffff;
    --bg-accent: #f4f1ec;
    --text-primary: #3d2914;
    --text-secondary: #6b4e24;
    --text-muted: #8b6914;
    --border-color: #e6d5b7;
    --accent-primary: #8b4513;
    --accent-secondary: #d2691e;
    --accent-gradient: linear-gradient(45deg, #8b4513, #d2691e);
    --shadow-color: rgba(139, 69, 19, 0.1);
    --border-radius: 12px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* Corporate Theme */
.theme-corporate {
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-accent: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --accent-primary: #3b82f6;
    --accent-secondary: #1d4ed8;
    --accent-gradient: linear-gradient(45deg, #3b82f6, #1d4ed8);
    --shadow-color: rgba(59, 130, 246, 0.1);
    --border-radius: 6px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* Restaurant Theme */
.theme-restaurant {
    --bg-primary: #fef2f2;
    --bg-secondary: #ffffff;
    --bg-accent: #fee2e2;
    --text-primary: #7f1d1d;
    --text-secondary: #991b1b;
    --text-muted: #b91c1c;
    --border-color: #fecaca;
    --accent-primary: #dc2626;
    --accent-secondary: #ea580c;
    --accent-gradient: linear-gradient(45deg, #dc2626, #ea580c);
    --shadow-color: rgba(220, 38, 38, 0.1);
    --border-radius: 8px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}

/* Health/Organic Theme */
.theme-health {
    --bg-primary: #f0fdf4;
    --bg-secondary: #ffffff;
    --bg-accent: #dcfce7;
    --text-primary: #14532d;
    --text-secondary: #166534;
    --text-muted: #15803d;
    --border-color: #bbf7d0;
    --accent-primary: #16a34a;
    --accent-secondary: #059669;
    --accent-gradient: linear-gradient(45deg, #16a34a, #059669);
    --shadow-color: rgba(22, 163, 74, 0.1);
    --border-radius: 16px;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --brand-primary: var(--accent-primary);
    --brand-secondary: var(--accent-secondary);
}


/* Theme Transitions */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Mobile-First Responsive Design */

/* Enhanced touch targets for POS environment - larger targets for faster interaction */
.touch-target {
    min-height: 60px; /* Increased from 44px for POS use */
    min-width: 60px;
    touch-action: manipulation; /* Improves touch responsiveness */
    user-select: none; /* Prevents text selection on double-tap */
}

/* Mobile-optimized interface button */
.interface-button {
    min-width: 120px; /* Larger mobile width for better touch */
    min-height: 44px; /* Minimum touch target */
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1), all 0.3s ease;
}

@media (min-width: 640px) {
    .interface-button {
        min-width: 12rem; /* 192px - desktop width */
    }
}

/* Mobile-specific improvements */
@media (max-width: 639px) {
    /* Better mobile spacing */
    .mobile-spacing {
        padding: 1rem 0.75rem;
    }

    /* Larger mobile text */
    .mobile-text {
        font-size: 0.875rem;
        line-height: 1.5;
    }

    /* Mobile-friendly product grid */
    .mobile-product-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }


    /* Mobile input improvements */
    .mobile-input {
        padding: 0.875rem 1rem;
        font-size: 1rem;
    }

    /* Mobile button improvements */
    .mobile-button {
        padding: 0.875rem 1.25rem;
        font-size: 0.875rem;
        min-height: 44px;
    }
}

/* Enhanced Product Card Styling for POS Environment */
.product-card {
    min-height: 80px; /* Increased minimum height to match Food category buttons */
    height: 80px; /* Fixed height to ensure consistency across all categories */
    padding: 0.25rem 0.75rem; /* Further adjusted padding */
    border-radius: var(--border-radius);
    background-color: var(--bg-accent);
    border: 2px solid transparent;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Perfectly Consistent POS Category Tab Styling */
.category-tab {
    /* Remove all variable padding and spacing */
    padding: 0 !important;
    margin: 0 !important;
    border: 1px solid var(--border-color) !important;
    border-radius: 6px !important;
    font-size: 0.75rem !important; /* 12px */
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    cursor: pointer !important;
    /* EXACT fixed dimensions - no deviation allowed */
    height: 36px !important;
    width: 100px !important;
    min-width: 100px !important;
    max-width: 100px !important;
    min-height: 36px !important;
    max-height: 36px !important;
    /* Perfect centering and layout */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    overflow: hidden !important;
    white-space: nowrap !important;
    background-color: var(--bg-accent) !important;
    color: var(--text-primary) !important;
    box-sizing: border-box !important;
    flex-shrink: 0 !important; /* Prevent any flex changes */
    flex-grow: 0 !important; /* Prevent any flex changes */
    text-overflow: ellipsis !important; /* Handle text overflow */
    /* Add touch properties directly to category buttons */
    touch-action: manipulation !important;
    user-select: none !important;
}

/* Override any touch-target class interference specifically for category tabs */
.category-tab.touch-target {
    height: 36px !important;
    width: 100px !important;
    min-width: 100px !important;
    max-width: 100px !important;
    min-height: 36px !important;
    max-height: 36px !important;
}

/* Minimal POS Hover and Active States */
.category-tab:hover {
    background-color: var(--accent-primary) !important;
    color: white !important;
    border-color: var(--accent-primary) !important;
    transform: translateY(-1px) !important; /* Subtle lift effect */
    /* Maintain exact dimensions on hover */
    height: 36px !important;
    width: 100px !important;
}

.category-tab.active {
    background-color: var(--accent-primary) !important;
    color: white !important;
    border-color: var(--accent-primary) !important;
    /* Maintain exact dimensions when active */
    height: 36px !important;
    width: 100px !important;
}

/* Consistent icon styling */
.category-tab i {
    margin-right: 0.25rem !important; /* 4px spacing - tighter for compact design */
    font-size: 0.75rem !important; /* Same size as text for consistency */
    transition: opacity 0.2s ease !important;
    flex-shrink: 0 !important; /* Prevent icon from shrinking */
    width: 12px !important; /* Fixed width for alignment */
    text-align: center !important; /* Center icons within their space */
    display: inline-block !important; /* Ensure proper icon display */
}

/* Simple active feedback */
.category-tab:active {
    transform: translateY(0) !important; /* Remove lift on press */
    opacity: 0.9 !important;
    /* Maintain exact dimensions when pressed */
    height: 36px !important;
    width: 100px !important;
}


.product-card:hover {
    transform: translateY(-2px);
    border-color: var(--accent-primary);
    box-shadow: 0 4px 12px var(--shadow-color);
}

.product-card:active {
    transform: scale(0.98);
    box-shadow: 0 2px 6px var(--shadow-color);
}

/* Ensure consistent content alignment within product cards */
.product-card .text-center {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
}

/* Ensure consistent icon sizing across all categories */
.product-card i {
    flex-shrink: 0;
}

/* Ensure text doesn't overflow and maintains consistent spacing */
.product-card h3,
.product-card p {
    margin: 0;
    line-height: 1.2;
}

/* Improve spacing in product grid for POS use */
#productContainer {
    gap: 1rem; /* Increased gap between products */
    max-height: 270px; /* Limit to approximately 3 rows (80px + 1rem gap per row) */
    overflow-y: auto;
    padding-right: 8px; /* Add space for scrollbar */
}

/* Custom scrollbar for product grid */
#productContainer::-webkit-scrollbar {
    width: 6px;
}

#productContainer::-webkit-scrollbar-track {
    background: var(--bg-accent);
    border-radius: 3px;
}

#productContainer::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

#productContainer::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}

@media (min-width: 768px) {
    #productContainer {
        gap: 1.5rem; /* Even more space on larger screens */
        max-height: 330px; /* Adjust for larger cards on desktop (100px + 1.5rem gap per row) */
    }

    .product-card {
        min-height: 100px; /* Increased height for better visual balance on desktop */
        height: 100px; /* Fixed height to ensure consistency across all categories */
        padding: 0.5rem 1rem;
    }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
    /* Remove hover effects on touch devices */
    .product-card:hover {
        transform: none;
        border-color: var(--border-color) !important;
        box-shadow: none;
    }

    /* Add touch feedback instead */
    .product-card:active {
        transform: scale(0.98);
        background-color: var(--accent-primary);
        color: white;
    }

    .quick-action:hover {
        background-color: transparent;
        border-color: var(--border-color);
        transform: none;
    }

    .quick-action:active {
        background-color: var(--bg-accent);
        transform: scale(0.98);
    }
}

/* Component Animations */
.message-bubble {
    animation: slideIn 0.3s ease-out;
}
@keyframes slideIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}
.product-card:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
    border-color: var(--accent-primary) !important;
    box-shadow: 0 4px 12px var(--shadow-color);
}
.typing-indicator {
    animation: pulse 1.5s infinite;
}
.fade-in {
    animation: fadeIn 0.5s ease-in;
}
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Quick Actions Enhanced Styling */
.quick-action {
    position: relative;
    overflow: hidden;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}
.quick-action:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: var(--accent-primary);
}
.quick-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}
.quick-action:hover::before {
    left: 100%;
}
.quick-action .icon-container {
    transition: all 0.2s ease;
}
.quick-action:hover .icon-container {
    transform: scale(1.1);
}
.quick-action:active {
    transform: scale(0.98);
}

.suggestion-item:hover {
    background: var(--accent-gradient);
    color: white;
    transform: scale(1.02);
    transition: all 0.2s ease;
}
.chat-container {
    height: 350px;
    max-height: 350px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

/* Mobile-responsive chat container */
@media (max-width: 639px) {
    .chat-container {
        max-height: 350px;
    }
}
.mode-toggle {
    background: var(--accent-gradient);
}
.theme-selector {
    background: var(--accent-gradient);
}

/* POS UI Improvements - Uniform Quick Actions (Fixed Width, No Scroll) */
.quick-actions-compact {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    padding: 0.25rem 0;
    width: 100%;
}

.quick-action-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 0.25rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
    transition: all 0.2s ease;
    cursor: pointer;
    height: 60px; /* Fixed height for all buttons */
    width: 100%; /* Ensure button fills container */
}

.quick-action-icon:hover {
    background-color: var(--bg-accent);
    border-color: var(--accent-primary);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px var(--shadow-color);
}

.quick-action-icon i {
    font-size: 1.25rem;
    margin-bottom: 0.25rem;
    color: var(--accent-primary);
}

.quick-action-icon span {
    font-size: 0.7rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.1;
    white-space: nowrap;
}

/* Dropdown positioning - appear above buttons to avoid scrolling */
.quick-action-dropdown {
    position: absolute;
    bottom: 100%; /* Position above the button */
    left: 0;
    margin-bottom: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
    box-shadow: 0 -4px 12px var(--shadow-color);
    z-index: 1000;
    min-width: 160px;
}

/* Adaptive Current Order Container */
.order-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 200px);
    max-height: 800px;
    min-height: 400px;
}


.order-items-list {
    flex-grow: 1;
    overflow-y: auto;
    min-height: 350px;
    padding: 0;
}

.order-actions-sticky {
    flex-shrink: 0;
    padding: 16px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* Custom scrollbar for order items */
.order-items-list::-webkit-scrollbar {
    width: 6px;
}

.order-items-list::-webkit-scrollbar-track {
    background: var(--bg-accent);
    border-radius: 3px;
}

.order-items-list::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

.order-items-list::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
}

/* Scroll fade indicators */
.order-items-list {
    position: relative;
}

.order-items-list::before,
.order-items-list::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    height: 10px;
    z-index: 1;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.order-items-list::before {
    top: 0;
    background: linear-gradient(to bottom, var(--bg-secondary), transparent);
}

.order-items-list::after {
    bottom: 0;
    background: linear-gradient(to top, var(--bg-secondary), transparent);
}

.order-items-list.has-scroll::before,
.order-items-list.has-scroll::after {
    opacity: 1;
}

/* Conversation area height matching Current Order */
#conversationArea {
    min-height: 600px;
    display: flex;
    flex-direction: column;
}

#conversationArea .p-6 {
    flex: 1;
    display: flex;
    flex-direction: column;
}

#chatContainer {
    height: 350px;
    min-height: 350px;
    max-height: 350px;
    overflow-y: auto;
}

/* Responsive adjustments to maintain fixed width and uniform sizing */
@media (max-width: 768px) {
    .quick-actions-compact {
        gap: 0.75rem;
    }

    .quick-action-icon {
        padding: 0.6rem 0.2rem;
        height: 58px; /* Slightly smaller but still uniform */
    }

    .quick-action-icon i {
        font-size: 1.1rem;
    }

    .quick-action-icon span {
        font-size: 0.65rem;
    }
}

/* Tablet-specific order list height */
@media (min-width: 640px) and (max-width: 1023px) {
    .order-items-list {
        min-height: 450px;
        max-height: 600px;
    }
}

@media (max-width: 480px) {
    .quick-actions-compact {
        gap: 0.5rem;
    }

    .quick-action-icon {
        padding: 0.5rem 0.15rem;
        height: 55px; /* Smaller but still uniform */
    }

    .quick-action-icon i {
        font-size: 1rem;
        margin-bottom: 0.2rem;
    }

    .quick-action-icon span {
        font-size: 0.6rem;
    }
}


/* Horizontal Quick Actions */
.horizontal-actions {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 0.5rem 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.horizontal-actions::-webkit-scrollbar {
    display: none;
}

.horizontal-action {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-secondary);
    min-width: 80px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.horizontal-action:hover {
    background-color: var(--bg-accent);
    border-color: var(--accent-primary);
}

.horizontal-action i {
    font-size: 1.25rem;
    color: var(--accent-primary);
    margin-bottom: 0.25rem;
}

.horizontal-action span {
    font-size: 0.75rem;
    color: var(--text-secondary);
    text-align: center;
    line-height: 1.2;
}

/* Compact Order Summary */
.compact-summary {
    background-color: var(--bg-accent);
    border-radius: var(--border-radius);
    padding: 0.75rem;
    margin-bottom: 1rem;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
}

.summary-row:last-child {
    margin-bottom: 0;
    padding-top: 0.5rem;
    border-top: 1px solid var(--border-color);
    font-weight: 600;
}

/* Enhanced Order Item Cards */
.order-item-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    min-height: 100px; /* Default mode: ~100-120px total */
}

/* Compact mode styling */
.order-items-list[style*="250px"] .order-item-card {
    padding: 8px 12px;
    margin-bottom: 6px;
    min-height: 80px; /* Compact mode: ~80-100px total */
    border-radius: 6px;
}

.order-item-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-color: var(--accent-primary);
}

.order-item-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
    gap: 12px;
}

/* Compact mode header */
.order-items-list[style*="250px"] .order-item-header {
    margin-bottom: 6px;
    gap: 8px;
}

.product-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

/* Compact mode icon */
.order-items-list[style*="250px"] .product-icon {
    width: 28px;
    height: 28px;
    border-radius: 6px;
    font-size: 12px;
    margin-right: 8px;
}

.product-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.product-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text-primary);
    margin-bottom: 3px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Compact mode product name */
.order-items-list[style*="250px"] .product-name {
    font-size: 0.875rem;
    line-height: 1.1;
    margin-bottom: 2px;
}

.product-unit-price {
    font-size: 0.8rem;
    color: var(--text-muted);
    line-height: 1;
}

/* Compact mode unit price */
.order-items-list[style*="250px"] .product-unit-price {
    font-size: 0.75rem;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 8px;
}

/* Compact mode quantity controls */
.order-items-list[style*="250px"] .quantity-controls {
    gap: 8px;
    margin-top: 6px;
}

.quantity-btn {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: none;
    background-color: var(--bg-accent);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 11px;
}

/* Compact mode quantity buttons */
.order-items-list[style*="250px"] .quantity-btn {
    width: 24px;
    height: 24px;
    font-size: 10px;
}

.quantity-btn:hover {
    background-color: var(--accent-primary);
    color: white;
    transform: scale(1.1);
}

.quantity-btn:active {
    transform: scale(0.95);
}

.quantity-display {
    min-width: 36px;
    text-align: center;
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text-primary);
}

/* Compact mode quantity display */
.order-items-list[style*="250px"] .quantity-display {
    min-width: 32px;
    font-size: 0.875rem;
}

.item-total-price {
    font-weight: 700;
    font-size: 1rem;
    color: var(--text-primary);
    margin-left: auto;
    text-align: right;
    min-width: 60px;
    flex-shrink: 0;
}

/* Compact mode item total price */
.order-items-list[style*="250px"] .item-total-price {
    font-size: 0.9rem;
    min-width: 55px;
}

.remove-item-btn {
    width: 26px;
    height: 26px;
    border-radius: 6px;
    border: none;
    background-color: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 8px;
    font-size: 11px;
    flex-shrink: 0;
}

/* Compact mode remove button */
.order-items-list[style*="250px"] .remove-item-btn {
    width: 22px;
    height: 22px;
    font-size: 10px;
    border-radius: 4px;
    margin-left: 6px;
}

.remove-item-btn:hover {
    background-color: rgba(239, 68, 68, 0.2);
    transform: scale(1.1);
}

.empty-order-state {
    text-align: center;
    padding: 48px 24px;
    color: var(--text-muted);
}

.empty-order-icon {
    font-size: 48px;
    opacity: 0.5;
    margin-bottom: 16px;
}

.empty-order-text {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.empty-order-subtext {
    font-size: 14px;
    opacity: 0.8;
}

/* Product icon color variations */
.product-icon.coffee { background: linear-gradient(135deg, #8B4513, #A0522D); }
.product-icon.latte { background: linear-gradient(135deg, #D2691E, #F4A460); }
.product-icon.croissant { background: linear-gradient(135deg, #DAA520, #FFD700); }
.product-icon.muffin { background: linear-gradient(135deg, #CD853F, #DEB887); }
.product-icon.sandwich { background: linear-gradient(135deg, #228B22, #32CD32); }
.product-icon.juice { background: linear-gradient(135deg, #FF6347, #FF7F50); }

/* Animation for adding/removing items */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideOutDown {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(20px);
    }
}

.order-item-card.adding {
    animation: slideInUp 0.3s ease-out;
}

.order-item-card.removing {
    animation: slideOutDown 0.3s ease-out;
}

/* Quantity change pulse effect */
.quantity-pulse {
    animation: pulse 0.4s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
    